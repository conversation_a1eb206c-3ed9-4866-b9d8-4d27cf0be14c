<?php
/**
 * Test script for customer registration
 * Tests the customer account creation at https://ppn.project/b2b/customer/account/create/
 */

// Configuration
$baseUrl = 'https://ppn.project';
$registrationUrl = $baseUrl . '/b2b/customer/account/create/';
$createPostUrl = $baseUrl . '/customer/account/createpost/';

// Test data
$testCustomer = [
    'firstname' => 'Test',
    'lastname' => 'Customer',
    'email' => 'test.customer.' . time() . '@example.com',
    'password' => 'TestPassword123!',
    'password_confirmation' => 'TestPassword123!',
    'is_subscribed' => '0',
    // Address fields
    'create_address' => '1',
    'street' => ['123 Test Street'],
    'city' => 'Test City',
    'country_id' => 'US',
    'region_id' => '12', // California
    'postcode' => '90210',
    'telephone' => '************',
    'default_billing' => '1',
    'default_shipping' => '1'
];

echo "=== Customer Registration Test ===\n";
echo "Testing URL: $registrationUrl\n";
echo "Test Email: " . $testCustomer['email'] . "\n\n";

// Initialize cURL session
$ch = curl_init();

// Set cURL options for getting the registration form
curl_setopt_array($ch, [
    CURLOPT_URL => $registrationUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_COOKIEJAR => '/tmp/cookies.txt',
    CURLOPT_COOKIEFILE => '/tmp/cookies.txt',
    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
]);

echo "Step 1: Loading registration form...\n";
$formHtml = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if ($httpCode !== 200) {
    echo "ERROR: Failed to load registration form. HTTP Code: $httpCode\n";
    curl_close($ch);
    exit(1);
}

echo "✓ Registration form loaded successfully\n";

// Extract form key from the HTML
if (preg_match('/name="form_key"\s+value="([^"]+)"/', $formHtml, $matches)) {
    $formKey = $matches[1];
    echo "✓ Form key extracted: $formKey\n";
} else {
    echo "ERROR: Could not extract form key from registration form\n";
    curl_close($ch);
    exit(1);
}

// Check if address fields are present
if (strpos($formHtml, 'Address Information') !== false) {
    echo "✓ Address fields are enabled in registration form\n";
} else {
    echo "⚠ Address fields not found in registration form\n";
}

// Check if country dropdown has "Please select" option
if (strpos($formHtml, 'Please select a country') !== false) {
    echo "✓ Country dropdown has 'Please select' option\n";
} else {
    echo "⚠ Country dropdown missing 'Please select' option\n";
}

// Prepare POST data
$postData = array_merge($testCustomer, ['form_key' => $formKey]);

echo "\nStep 2: Submitting registration form...\n";

// Set cURL options for POST request
curl_setopt_array($ch, [
    CURLOPT_URL => $createPostUrl,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => http_build_query($postData),
    CURLOPT_HEADER => true,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => false, // Don't follow redirects automatically
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$redirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);

echo "HTTP Response Code: $httpCode\n";

// Split headers and body
list($headers, $body) = explode("\r\n\r\n", $response, 2);

// Check for success indicators
if ($httpCode === 302) {
    echo "✓ Registration form submitted (redirect response)\n";
    
    if ($redirectUrl) {
        echo "Redirect URL: $redirectUrl\n";
        
        // Check if redirected to success page or account page
        if (strpos($redirectUrl, 'account') !== false && strpos($redirectUrl, 'create') === false) {
            echo "✓ Redirected to account area - likely successful registration\n";
        } elseif (strpos($redirectUrl, 'create') !== false) {
            echo "⚠ Redirected back to registration form - possible validation error\n";
        }
    }
} elseif ($httpCode === 200) {
    echo "⚠ No redirect - checking response content for errors\n";
    
    // Check for common error messages
    if (strpos($body, 'Delete operation is forbidden') !== false) {
        echo "❌ ERROR: 'Delete operation is forbidden' error detected!\n";
    } elseif (strpos($body, 'error') !== false || strpos($body, 'Error') !== false) {
        echo "⚠ Possible error in response content\n";
    } else {
        echo "✓ No obvious errors detected in response\n";
    }
} else {
    echo "❌ ERROR: Unexpected HTTP response code: $httpCode\n";
}

// Follow redirect to check final result
if ($redirectUrl && $httpCode === 302) {
    echo "\nStep 3: Following redirect to check final result...\n";
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $redirectUrl,
        CURLOPT_POST => false,
        CURLOPT_POSTFIELDS => null,
        CURLOPT_HEADER => false,
        CURLOPT_FOLLOWLOCATION => true,
    ]);
    
    $finalResponse = curl_exec($ch);
    $finalHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    echo "Final page HTTP Code: $finalHttpCode\n";
    
    if (strpos($finalResponse, 'Thank you for registering') !== false || 
        strpos($finalResponse, 'Welcome') !== false ||
        strpos($finalResponse, 'My Account') !== false) {
        echo "✓ SUCCESS: Customer registration appears to be successful!\n";
    } elseif (strpos($finalResponse, 'Delete operation is forbidden') !== false) {
        echo "❌ FAILED: 'Delete operation is forbidden' error on final page!\n";
    } else {
        echo "⚠ UNCLEAR: Could not determine final registration status\n";
    }
}

curl_close($ch);

echo "\n=== Test Summary ===\n";
echo "Test completed for email: " . $testCustomer['email'] . "\n";
echo "Check the Magento admin panel to verify if the customer was created.\n";
echo "If successful, you should see the customer in Customers > All Customers\n";
