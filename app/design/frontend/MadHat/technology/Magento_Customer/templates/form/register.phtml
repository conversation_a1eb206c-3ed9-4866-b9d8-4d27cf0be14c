<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Customer\CreateAccountButton;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Customer\Block\Form\Register;
use Magento\Customer\Block\Widget\Dob;
use Magento\Customer\Block\Widget\Gender;
use Magento\Customer\Block\Widget\Name as NameWidget;
use Magento\Customer\Block\Widget\Taxvat;
use Magento\Customer\Helper\Address;
use Magento\Framework\Escaper;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper
// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Register $block */
/** @var Escaper $escaper */
/** @var ReCaptcha $recaptcha */
/** @var CreateAccountButton $createAccountButtonViewModel */
/** @var ViewModelRegistry $viewModels */
/** @var HeroiconsSolid $heroicons */

$formId = 'accountcreate';

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
$recaptcha = $block->getData('viewModelRecaptcha');
$heroicons = $viewModels->require(HeroiconsSolid::class);
$createAccountButtonViewModel = $viewModels->require(CreateAccountButton::class);
$region = $block->getAttributeData()->getFrontendLabel('region');
$selectRegion = 'Please select a region, state or province.';
$showOptionalRegions = $block->getConfig('general/region/display_all');
$regionLabel = $block->getAttributeData()->getFrontendLabel('region');
$minimumPasswordLength = $block->getMinimumPasswordLength();
$passwordMinCharacterSets = $block->getRequiredCharacterClassesNumber();
?>
<div class="pb-4">
    <?php /* Extensions placeholder */ ?>
    <?= $block->getChildHtml('customer.form.register.extra') ?>
    <form class="form create account form-create-account"
          action="<?= $escaper->escapeUrl($block->getPostActionUrl()) ?>"
          x-data="Object.assign(hyva.formValidation($el), initForm())"
          <?php if ($block->getShowAddressFields()): ?>
          @private-content-loaded.window="onPrivateContentLoaded(event.detail.data)"
          <?php endif; ?>
          id="<?= $escaper->escapeHtmlAttr($formId) ?>"
          @submit.prevent="submitForm()"
          method="post" id="form-validate" enctype="multipart/form-data" autocomplete="off"
    >
        <?= /* @noEscape */ $block->getBlockHtml('formkey'); ?>
        <?= $block->getChildHtml('form_fields_before') ?>
        <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_CREATE) : '' ?>
        <!-- <div class="md:grid grid-cols-2 gap-4"> -->
            <fieldset class="my-4 card card md:grid grid-cols-2 gap-4">
                <legend class="col-span-2">
                    <span class="text-2xl font-semibold leading-10 text-cgrey-90">
                        <?= $escaper->escapeHtml(__('Sign-in Information')) ?></span>
                </legend>
                <div class="field field-reserved required col-span-2">
                    <label for="email_address" class="label text-cgrey-90">
                        <span>
                            <?= $escaper->escapeHtml(__('Email')) ?>
                        </span>
                    </label>
                    <div class="control">
                        <input
                            type="email"
                            name="email"
                            autocomplete="email"
                            id="email_address"
                            required
                            value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getEmail()) ?>"
                            title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                            class="form-input w-full"
                            @input.debounce="onChange"
                        />
                    </div>
                </div>

                <?php
                // Invoice Email Field - MadHat Custom
                $invoiceEmailHelper = $block->getLayout()->createBlock(\MadHat\Customer\Block\Form\InvoiceEmail::class);
                if ($invoiceEmailHelper && $invoiceEmailHelper->isInvoiceEmailEnabled()):
                ?>
                <div class="field field-reserved col-span-2">
                    <label for="invoice_email" class="label text-cgrey-90">
                        <span><?= $escaper->escapeHtml(__('Invoice Email')) ?></span>
                    </label>
                    <div class="control">
                        <input
                            type="email"
                            name="invoice_email"
                            autocomplete="email"
                            id="invoice_email"
                            value="<?= $escaper->escapeHtmlAttr($invoiceEmailHelper->getInvoiceEmailValue()) ?>"
                            title="<?= $escaper->escapeHtmlAttr(__('Invoice Email')) ?>"
                            class="form-input w-full"
                            data-validate="{'validate-email':true}"
                            @input.debounce="onChange"
                        />
                    </div>
                </div>
                <?php endif; ?>

                <div class="field field-reserved required">
                    <label for="password" class="label text-cgrey-90">
                        <span>
                            <?= $escaper->escapeHtml(__('Password')) ?>
                        </span>
                    </label>
                    <div class="control flex items-center">
                        <?php $minimumPasswordLength = $block->getMinimumPasswordLength() ?>
                        <div class="sr-only" aria-live="polite">
                            <template x-if="!showPassword">
                                <span>
                                    <?= $escaper->escapeHtml(__('Password hidden')) ?>
                                </span>
                            </template>
                            <template x-if="showPassword">
                                <span>
                                    <?= $escaper->escapeHtml(__('Password shown')) ?>
                                </span>
                            </template>
                        </div>
                        <input
                            :type="showPassword ? 'text' : 'password'"
                            type="password"
                            id="password"
                            name="password"
                            title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                            minlength="<?= $escaper->escapeHtmlAttr($minimumPasswordLength) ?>"
                            class="form-input w-full"
                            required
                            data-validate='{"password-strength": {"minCharacterSets": <?= (int) $passwordMinCharacterSets ?>}}'
                            @input.debounce="onChange"
                            autocomplete="off"
                        >
                        <button
                            type="button"
                            :aria-pressed="showPassword ? true : false"
                            x-on:click="showPassword = !showPassword"
                            class="px-4 py-3"
                            :aria-label="showPassword ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'"
                        >
                            <template x-if="!showPassword">
                                <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPassword">
                                <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </button>
                    </div>
                    <div
                        id="password-strength-meter-container"
                        data-role="password-strength-meter"
                        aria-live="polite"
                    >
                        <div id="password-strength-meter" class="password-strength-meter">
                            <?= $escaper->escapeHtml(__('Password Strength')) ?>:
                            <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                                <?= $escaper->escapeHtml(__('No Password')) ?>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="field field-reserved required">
                    <label for="password-confirmation" class="label  text-cgrey-90">
                        <span>
                            <?= $escaper->escapeHtml(__('Confirm Password')) ?>
                        </span>
                    </label>
                    <div class="control flex items-center">
                        <div class="sr-only" aria-live="polite">
                            <template x-if="!showPasswordConfirm">
                                <span>
                                    <?= $escaper->escapeHtml(__('Confirm password hidden')) ?>
                                </span>
                            </template>
                            <template x-if="showPasswordConfirm">
                                <span>
                                    <?= $escaper->escapeHtml(__('confirm password shown')) ?>
                                </span>
                            </template>
                        </div>
                        <input
                            :type="showPasswordConfirm ? 'text' : 'password'"
                            type="password"
                            name="password_confirmation"
                            title="<?= $escaper->escapeHtmlAttr(__('Confirm Password')) ?>"
                            id="password-confirmation"
                            data-validate='{"equalTo": "password"}'
                            @input.debounce="onChange"
                            required
                            class="form-input w-full"
                            autocomplete="off"
                        >
                        <button
                            type="button"
                            x-on:click="showPasswordConfirm = !showPasswordConfirm"
                            :aria-pressed="showPasswordConfirm ? true : false"
                            class="px-4 py-3"
                            :aria-label="
                                showPasswordConfirm ? '<?= $escaper->escapeJs(__('Hide Password')) ?>' : '<?= $escaper->escapeJs(__('Show Password')) ?>'
                            "
                        >
                            <template x-if="!showPasswordConfirm">
                                <?= $heroicons->eyeHtml('w-5 h-5'); ?>
                            </template>
                            <template x-if="showPasswordConfirm">
                                <?= $heroicons->eyeOffHtml('w-5 h-5'); ?>
                            </template>
                        </button>
                    </div>
                </div>
                <?= $block->getChildHtml('form_additional_info') ?>
                <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_CREATE) : '' ?>
                <div class="col-span-2">
                    <input type="hidden" name="success_url" value="<?= $escaper->escapeUrl($block->getSuccessUrl()) ?>">
                    <input type="hidden" name="error_url" value="<?= $escaper->escapeUrl($block->getErrorUrl()) ?>">

                    <?php if ($block->isNewsletterEnabled()): ?>
                        <div class="field choice newsletter">
                            <input type="checkbox" name="is_subscribed"
                                title="<?= $escaper->escapeHtmlAttr(__('Sign Up for Newsletter')) ?>" value="1"
                                id="is_subscribed"
                                    <?php if ($block->getFormData()->getIsSubscribed()): ?>
                                        checked="checked"
                                    <?php endif; ?>
                                class="checkbox">
                            <label for="is_subscribed" class="label  text-cgrey-90">
                                <span>
                                    <?= $escaper->escapeHtml(__('Sign Up for Newsletter')) ?>
                                </span>
                            </label>
                        </div>
                        <?php /* Extensions placeholder */ ?>
                        <?= $block->getChildHtml('customer.form.register.newsletter') ?>
                    <?php endif ?>

                    <?php $dob = $block->getLayout()->createBlock(Dob::class) ?>
                    <?php if ($dob->isEnabled()): ?>
                        <?= $dob->setDate($block->getFormData()->getDob())->toHtml() ?>
                    <?php endif ?>

                    <?php $taxvat = $block->getLayout()->createBlock(Taxvat::class) ?>
                    <?php if ($taxvat->isEnabled()): ?>
                        <?= $taxvat->setTaxvat($block->getFormData()->getTaxvat())->toHtml() ?>
                    <?php endif ?>

                    <?php $gender = $block->getLayout()->createBlock(Gender::class) ?>
                    <?php if ($gender->isEnabled()): ?>
                        <?= $gender->setGender($block->getFormData()->getGender())->toHtml() ?>
                    <?php endif ?>
                    <?= $block->getChildHtml('fieldset_create_info_additional') ?>
                </div>
            </fieldset>
        <!-- </div> -->

        <?php if ($block->getShowAddressFields()): ?>
            <fieldset class="my-4 card md:grid grid-cols-2 gap-4">
                <legend class="col-span-2 text-2xl font-semibold">
                    <span>
                        <?= $escaper->escapeHtml(__('Address Information')) ?>
                    </span>
                </legend>
                <input type="hidden" name="create_address" value="1"/>

                <?= $block->getLayout()->createBlock(NameWidget::class)->setObject($block->getFormData())->setForceUseCustomerAttributes(true)->toHtml() ?>

                <?php $company = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Company::class) ?>
                <?php if ($company->isEnabled()): ?>
                    <?= $company->setCompany($block->getFormData()->getCompany())->toHtml() ?>
                <?php endif ?>

                <?php $taxvatWidget = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Taxvat::class) ?>
                <?php if ($taxvatWidget->isEnabled()): ?>
                    <?= $taxvatWidget->setTaxvat($block->getFormData()->getTaxvat())->setFieldIdFormat('taxvat')->setFieldName('taxvat')->toHtml() ?>
                <?php endif ?>

                <?php $telephone = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Telephone::class) ?>
                <?php if ($telephone->isEnabled()): ?>
                    <?= $telephone->setTelephone($block->getFormData()->getTelephone())->toHtml() ?>
                <?php endif ?>

                <?php $fax = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Fax::class) ?>
                <?php if ($fax->isEnabled()): ?>
                    <?= $fax->setFax($block->getFormData()->getFax())->toHtml() ?>
                <?php endif ?>
                <?php $streetValidationClass =
                    $this->helper(Address::class)->getAttributeValidationClass(
                        'street'
                    ); ?>

                <div class="field field-reserved street required">
                    <label for="street_1" class="label"><span><?= /* @noEscape */
                            $block->getAttributeData()->getFrontendLabel('street') ?></span></label>
                    <div class="control">
                        <input type="text" name="street[]"
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getStreet(0)) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('street') ?>"
                               id="street_1"
                               class="form-input w-full <?= $escaper->escapeHtmlAttr($streetValidationClass) ?>">

                    </div>
                </div>

                <div class="field field-reserved street ">
                    <?php $streetValidationClass =
                        trim(str_replace('required-entry', '', $streetValidationClass)); ?>
                    <?php for ($i = 2, $n = $this->helper(Address::class)->getStreetLines(); $i <= $n; $i++): ?>
                            <label class="label" for="street_<?= /* @noEscape */ $i ?>">
                                <span><?= $escaper->escapeHtml(__('Address')) ?></span>
                            </label>
                            <div class="control">
                                <input type="text" name="street[]"
                                        value="<?= $escaper->escapeHtmlAttr($block
                                            ->getFormData()
                                            ->getStreetLine($i - 1)) ?>"
                                        title="<?= $escaper
                                            ->escapeHtmlAttr(__('Street Address %1', $i)) ?>"
                                        id="street_<?= (int) $i ?>"
                                        class="form-input w-full <?= $escaper
                                            ->escapeHtmlAttr($streetValidationClass) ?>"
                                >
                            </div>
                    <?php endfor; ?>
                </div>

                <div class="field field-reserved">
                    <label for="city" class="label">
                        <span>
                            <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>
                        </span>
                    </label>
                    <div class="control">
                        <input type="text" name="city"
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getCity()) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>"
                               class="form-input w-full <?= $escaper->escapeHtmlAttr($this
                                   ->helper(Address::class)
                                   ->getAttributeValidationClass('city')) ?>"
                               id="city">

                    </div>
                </div>

                <div class="field field-reserved region required w-full"
                      x-cloak
                      x-show="(hasAvailableRegions() && isRegionRequired) || showOptionalRegions"
                >
                    <label class="label" for="region_id">
                        <span><?= /* @noEscape */ $regionLabel ?></span>
                    </label>
                    <div class="control">
                        <template x-if="hasAvailableRegions() && (isRegionRequired || showOptionalRegions)">
                            <select id="region_id" name="region_id"
                                    title="<?= /* @noEscape */ $regionLabel ?>"
                                    class="form-select w-full validate-select region_id"
                                    x-ref="region_id"
                                    x-model="selectedRegion"
                                    @change="$refs.region.value = availableRegions[selectedRegion].name"
                            >
                                <option value=""><?= $escaper->escapeHtml(__('Please select a region, state or province.')) ?></option>
                                <template x-for="regionId in Object.keys(availableRegions)">
                                    <?php /* in alpine v3, if the bound props update, the template body gets evaluated before the template condition */ ?>
                                    <?php /* because of this it is required to check if availableRegions[regionId] is set */ ?>
                                    <option :value="regionId"
                                            :name="availableRegions[regionId] && availableRegions[regionId].name"
                                            x-text="availableRegions[regionId] && availableRegions[regionId].name"
                                            :selected="selectedRegion === regionId"
                                    >
                                    </option>
                                </template>
                            </select>
                        </template>
                        <input :type="hasAvailableRegions() && (isRegionRequired || showOptionalRegions) ? 'hidden' : 'text'"
                               id="region"
                               name="region"
                               x-ref="region"
                               value="<?= $escaper->escapeHtmlAttr($block->getRegion()) ?>"
                               title="<?= /* @noEscape */ $regionLabel ?>"
                               class="form-input w-full"
                               :required="isRegionRequired"
                        />
                    </div>
                </div>

                <div class="field field-reserved required zip w-full">
                    <label class="label" for="zip">
                    <span>
                        <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>
                    </span>
                    </label>
                    <div class="control">
                        <input type="text"
                               name="postcode"
                               x-ref="postcode"
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getPostcode()) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>"
                               id="zip"
                               :required="isZipRequired"
                               @change="onChange"
                               data-validate='{"postcode": true}'
                        class="form-input w-full validate-zip-international
                        <?= $escaper->escapeHtmlAttr($this->helper(Address::class)->getAttributeValidationClass('postcode')) ?>">
                    </div>
                </div>

                <div class="field field-reserved country required w-full">
                    <label class="label" for="country">
                    <span>
                        <?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('country_id') ?>
                    </span>
                    </label>
                    <div class="control">
                        <?php $countries = $block
                            ->getCountryCollection()
                            ->setForegroundCountries($block->getTopDestinations())
                            ->toOptionArray();
                        ?>
                        <select name="country_id"
                                id="country"
                                title="Country"
                                required
                                class="form-select w-full"
                                x-ref="country_id"
                                @change="changeCountry($event.target)"
                        >
                            <option value=""><?= $escaper->escapeHtml(__('Please select a country')) ?></option>
                            <?php foreach ($countries as $country): ?>
                                <option name="<?= /** @noEscape */ $country['label'] ?>"
                                        value="<?= /** @noEscape */ $country['value'] ?>"
                                        data-is-zip-required="<?= (isset($country['is_zipcode_optional'])) ? '0' : '1' ?>"
                                        data-is-region-required="<?= (isset($country['is_region_required'])) ? '1' : '0' ?>"
                                    <?= ($block->getCountryId() ===  $country['value']) ? 'selected="selected"' : '' ?>
                                >
                                    <?= /** @noEscape */ $country['label'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <?php /* Removed custom address attributes to prevent duplicate country text field */ ?>
                <?php /* $addressAttributes = $block->getChildBlock('customer_form_address_user_attributes'); ?>
                <?php if ($addressAttributes): ?>
                    <?php $addressAttributes->setEntityType('customer_address'); ?>
                    <?php $addressAttributes->setFieldIdFormat('address:%1$s')->setFieldNameFormat('address[%1$s]'); ?>
                    <?php $block->restoreSessionData($addressAttributes->getMetadataForm(), 'address'); ?>
                    <?= $addressAttributes->setShowContainer(false)->toHtml() ?>
                <?php endif; */ ?>

                <div class="field choice col-span-2" data-mage-init='{"collapsible":{"openedState": "active", "active": false}}'>
                    <input type="checkbox" name="separate_shipping_address" id="separate_shipping_address" value="1" title="<?= $escaper->escapeHtmlAttr(__('The delivery address is different from the billing address.')) ?>" class="checkbox" x-model="showSeparateShippingAddress">
                    <label for="separate_shipping_address" class="label"><span><?= $escaper->escapeHtml(__('The delivery address is different from the billing address.')) ?></span></label>
                </div>

                <input type="hidden" name="default_billing" value="1">
                <input type="hidden" name="default_shipping" :value="showSeparateShippingAddress ? '0' : '1'">
            </fieldset>

            <fieldset class="my-4 card md:grid grid-cols-2 gap-4" x-show="showSeparateShippingAddress" x-cloak>
                <legend class="col-span-2 text-2xl font-semibold">
                    <span>
                        <?= $escaper->escapeHtml(__('Shipping Address')) ?>
                    </span>
                </legend>
                <input type="hidden" name="create_shipping_address" value="1"/> <!-- Assuming this might be needed -->

                <?php $shippingCompany = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Company::class) ?>
                <?php if ($shippingCompany->isEnabled()): ?>
                    <?= $shippingCompany->setCompany($block->getFormData()->getShippingCompany())->setFieldIdFormat('shipping_company')->setFieldNameFormat('shipping_company')->toHtml() ?>
                <?php endif ?>

                <?php $shippingTelephone = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Telephone::class) ?>
                <?php if ($shippingTelephone->isEnabled()): ?>
                    <?= $shippingTelephone->setTelephone($block->getFormData()->getShippingTelephone())->setFieldIdFormat('shipping_telephone')->setFieldNameFormat('shipping_telephone')->toHtml() ?>
                <?php endif ?>

                <?php $shippingFax = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Fax::class) ?>
                <?php if ($shippingFax->isEnabled()): ?>
                    <?= $shippingFax->setFax($block->getFormData()->getShippingFax())->setFieldIdFormat('shipping_fax')->setFieldNameFormat('shipping_fax')->toHtml() ?>
                <?php endif ?>

                <?php $shippingStreetValidationClass = $this->helper(Address::class)->getAttributeValidationClass('street'); ?>
                <div class="field field-reserved street required">
                    <label for="shipping_street_1" class="label"><span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('street') ?></span></label>
                    <div class="control">
                        <input type="text" name="shipping_street[]"
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getShippingStreet(0)) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('street') ?>"
                               id="shipping_street_1"
                               class="form-input w-full <?= $escaper->escapeHtmlAttr($shippingStreetValidationClass) ?>">
                    </div>
                </div>
                <div class="field field-reserved">
                    <?php $shippingStreetValidationClass = trim(str_replace('required-entry', '', $shippingStreetValidationClass)); ?>
                    <?php for ($i = 2, $n = $this->helper(Address::class)->getStreetLines(); $i <= $n; $i++): ?>
                        <label class="label" for="shipping_street_<?= /* @noEscape */ $i ?>">
                            <span><?= $escaper->escapeHtml(__('Address')) ?></span></label>
                        <div class="control">
                            <input type="text" name="shipping_street[]"
                                    value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getShippingStreetLine($i - 1)) ?>"
                                    title="<?= $escaper->escapeHtmlAttr(__('Street Address %1', $i)) ?>"
                                    id="shipping_street_<?= (int) $i ?>"
                                    class="form-input w-full <?= $escaper->escapeHtmlAttr($shippingStreetValidationClass) ?>">
                        </div>
                    <?php endfor; ?>
                </div>

                <div class="field field-reserved">
                    <label for="shipping_city" class="label">
                        <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?></span></label>
                    <div class="control">
                        <input type="text" name="shipping_city"
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getShippingCity()) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>"
                               class="form-input w-full <?= $escaper->escapeHtmlAttr($this->helper(Address::class)->getAttributeValidationClass('city')) ?>"
                               id="shipping_city">
                    </div>
                </div>

                <div class="field field-reserved region required w-full" x-cloak x-show="(hasShippingAvailableRegions() && isShippingRegionRequired) || showOptionalRegions">
                    <label class="label" for="shipping_region_id"><span><?= /* @noEscape */ $regionLabel ?></span></label>
                    <div class="control">
                        <template x-if="hasShippingAvailableRegions() && (isShippingRegionRequired || showOptionalRegions)">
                            <select id="shipping_region_id" name="shipping_region_id"
                                    title="<?= /* @noEscape */ $regionLabel ?>"
                                    class="form-select w-full validate-select region_id"
                                    x-ref="shipping_region_id"
                                    x-model="shippingSelectedRegion"
                                    @change="$refs.shipping_region.value = shippingAvailableRegions[shippingSelectedRegion].name">
                                <option value=""><?= $escaper->escapeHtml(__('Please select a region, state or province.')) ?></option>
                                <template x-for="regionId in Object.keys(shippingAvailableRegions)">
                                    <option :value="regionId"
                                            :name="shippingAvailableRegions[regionId] && shippingAvailableRegions[regionId].name"
                                            x-text="shippingAvailableRegions[regionId] && shippingAvailableRegions[regionId].name"
                                            :selected="shippingSelectedRegion === regionId">
                                    </option>
                                </template>
                            </select>
                        </template>
                        <input :type="hasShippingAvailableRegions() && (isShippingRegionRequired || showOptionalRegions) ? 'hidden' : 'text'"
                               id="shipping_region"
                               name="shipping_region"
                               x-ref="shipping_region"
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getShippingRegion()) ?>"
                               title="<?= /* @noEscape */ $regionLabel ?>"
                               class="form-input w-full"
                               :required="isShippingRegionRequired" />
                    </div>
                </div>

                <div class="field field-reserved zip required w-full">
                    <label class="label" for="shipping_zip"><span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?></span></label>
                    <div class="control">
                        <input type="text"
                               name="shipping_postcode"
                               x-ref="shipping_postcode"
                               value="<?= $escaper->escapeHtmlAttr($block->getFormData()->getShippingPostcode()) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>"
                               id="shipping_zip"
                               :required="isShippingZipRequired"
                               @change="onChange"
                               data-validate='{"postcode": true}'
                               class="form-input w-full validate-zip-international <?= $escaper->escapeHtmlAttr($this->helper(Address::class)->getAttributeValidationClass('postcode')) ?>">
                    </div>
                </div>

                <div class="field field-reserved country required w-full">
                    <label class="label" for="shipping_country_id"><span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('country_id') ?></span></label>
                    <div class="control">
                        <?php $shippingCountries = $block->getCountryCollection()->setForegroundCountries($block->getTopDestinations())->toOptionArray(); ?>
                        <select name="shipping_country_id"
                                id="shipping_country_id"
                                title="<?= $escaper->escapeHtmlAttr(__('Country')) ?>"
                                required
                                class="form-select w-full"
                                x-ref="shipping_country_id"
                                @change="changeShippingCountry($event.target)">
                            <option value=""><?= $escaper->escapeHtml(__('Please select a country')) ?></option>
                            <?php foreach ($shippingCountries as $country): ?>
                                <option name="<?= /** @noEscape */ $country['label'] ?>"
                                        value="<?= /** @noEscape */ $country['value'] ?>"
                                        data-is-zip-required="<?= (isset($country['is_zipcode_optional'])) ? '0' : '1' ?>"
                                        data-is-region-required="<?= (isset($country['is_region_required'])) ? '1' : '0' ?>"
                                    <?= ($block->getFormData()->getShippingCountryId() === $country['value']) ? 'selected="selected"' : '' ?>>
                                    <?= /** @noEscape */ $country['label'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <?php /* Removed custom shipping address attributes to prevent duplicate country text field */ ?>
                <?php /* $shippingAddressAttributes = $block->getChildBlock('customer_form_address_user_attributes'); ?>
                <?php if ($shippingAddressAttributes): ?>
                    <?php $shippingAddressAttributes->setEntityType('customer_address'); ?>
                    <?php $shippingAddressAttributes->setFieldIdFormat('shipping_address:%1$s')->setFieldNameFormat('shipping_address[%1$s]'); ?>
                    <?php $block->restoreSessionData($shippingAddressAttributes->getMetadataForm(), 'shipping_address'); ?>
                    <?= $shippingAddressAttributes->setShowContainer(false)->toHtml() ?>
                <?php endif; */ ?>
                <input type="hidden" name="default_shipping" value="1">
            </fieldset>
        <?php endif; ?>
        <div class="actions-toolbar flex px-4">
            <div class="primary">
                <button type="submit" class="action submit primary btn btn-primary disabled:opacity-75"
                        title="<?= $escaper->escapeHtmlAttr(__('Create an Account')) ?>"
                        <?php if ($createAccountButtonViewModel->disabled()): ?> disabled="disabled" data-recaptcha-btn<?php endif; ?>>
                        <span><?= $escaper->escapeHtml(__('Create an Account')) ?></span>
                </button>
            </div>
            <div class="secondary ml-4 self-center">
                <a class="action back text-cgrey-90 font-semibold"
                   href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                    <span>
                        <?= $escaper->escapeHtml(__('Back')) ?>
                    </span>
                </a>
            </div>
        </div>
    </form>

    <script>
        function initForm() {
            return {
                errors: 0,
                hasCaptchaToken: 0,
                showPassword: false,
                showPasswordConfirm: false,
                showSeparateShippingAddress: false,
                submitForm() {
                    this.validate()
                        .then(() => {
                            // Do not rename $form, the variable is expected to be declared in the recaptcha output
                            const $form = document.querySelector('#<?= $escaper->escapeJs($formId) ?>');
                            <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_CREATE) : '' ?>

                            if (this.errors === 0) {
                                $form.submit();
                            }
                        })
                        .catch((invalid) => {
                            if (invalid.length > 0) {
                                invalid[0].focus();
                            }
                        });
                },
                <?php if ($block->getShowAddressFields()): ?>
                directoryData: {},            // For billing address
                availableRegions: {},         // For billing address
                selectedRegion: <?= (int) $block->getFormData()->getRegionId() ?: '0' ?>, // For billing address
                isZipRequired: true,          // For billing address
                isRegionRequired: true,       // For billing address

                shippingDirectoryData: {},    // For shipping address
                shippingAvailableRegions: {}, // For shipping address
                shippingSelectedRegion: <?= (int) $block->getFormData()->getShippingRegionId() ?: '0' ?>, // For shipping address
                isShippingZipRequired: true,  // For shipping address
                isShippingRegionRequired: true, // For shipping address

                showOptionalRegions: <?= $showOptionalRegions ? 'true' : 'false' ?>,

                onPrivateContentLoaded(data) {
                    this.directoryData = data['directory-data'] || {};
                    this.shippingDirectoryData = data['directory-data'] || {}; // Initialize with same data for now

                    <?php $billingCountryId = $block->getFormData()->getCountryId(); ?>
                    <?php if ($billingCountryId): ?>
                        this.setCountry(this.$refs.country_id, '<?= $escaper->escapeJs($billingCountryId) ?>', '<?= $escaper->escapeJs($block->getFormData()->getRegion()) ?>');
                    <?php endif; ?>

                    <?php $shippingCountryId = $block->getFormData()->getShippingCountryId(); ?>
                    <?php if ($shippingCountryId): ?>
                        this.setShippingCountry(this.$refs.shipping_country_id, '<?= $escaper->escapeJs($shippingCountryId) ?>', '<?= $escaper->escapeJs($block->getFormData()->getShippingRegion()) ?>');
                    <?php elseif($billingCountryId): // Default shipping to billing country if not set ?>
                        // this.setShippingCountry(this.$refs.shipping_country_id, '<?= $escaper->escapeJs($billingCountryId) ?>', '<?= $escaper->escapeJs($block->getFormData()->getRegion()) ?>');
                    <?php endif; ?>
                },

                // Billing Address Functions
                setRegionInputValue(regionName) {
                    this.$nextTick(() => {
                        if (this.$refs.region) {
                            this.$refs.region.value = regionName;
                        }
                    });
                },
                setCountry(countrySelectElement, countryCode, initialRegionName = '') {
                    if (!countrySelectElement) return;
                    const selectedOption = Array.from(countrySelectElement.options).find(opt => opt.value === countryCode);
                    countrySelectElement.value = countryCode; // Ensure it's selected

                    const countryData = this.directoryData[countryCode] || false;

                    if (!countryData) {
                        this.setRegionInputValue('');
                        this.availableRegions = {};
                        this.selectedRegion = '0';
                        this.isRegionRequired = false; // Or based on config
                        this.isZipRequired = true; // Or based on config
                        return;
                    }

                    this.isZipRequired = selectedOption ? selectedOption.dataset.isZipRequired === '1' : true;
                    this.isRegionRequired = selectedOption ? selectedOption.dataset.isRegionRequired === '1' : true;
                    this.availableRegions = countryData.regions || {};

                    const initialRegion = Object.entries(this.availableRegions).find(([id, region]) => region.name === initialRegionName || region.code === initialRegionName);
                    this.selectedRegion = initialRegion ? initialRegion[0] : '0';
                    this.setRegionInputValue(initialRegion && this.availableRegions[initialRegion[0]] ? this.availableRegions[initialRegion[0]].name : '');
                },
                changeCountry(countrySelectElement) {
                    this.setCountry(countrySelectElement, countrySelectElement.value);
                    this.validateCountryDependentFields();
                },
                validateCountryDependentFields() {
                    this.$nextTick(() => {
                        if (this.fields.postcode) this.removeMessages(this.fields.postcode);
                        if (this.fields.region) this.removeMessages(this.fields.region);
                        delete this.fields.postcode;
                        delete this.fields.region; // remove to re-evaluate required status
                        if (this.$refs.country_id) this.setupField(this.$refs.country_id); // re-setup country for postcode validation context
                        if (this.$refs.postcode) this.setupField(this.$refs.postcode);
                        if (this.$refs.region) this.setupField(this.$refs.region);
                        if (this.$refs.region_id) this.setupField(this.$refs.region_id);


                        if (this.fields.postcode) this.validateField(this.fields.postcode);
                        if (this.fields.region) this.validateField(this.fields.region);
                         if (this.fields.region_id) this.validateField(this.fields.region_id);
                    });
                },
                hasAvailableRegions() {
                    return Object.keys(this.availableRegions).length > 0;
                },

                // Shipping Address Functions
                setShippingRegionInputValue(regionName) {
                    this.$nextTick(() => {
                        if (this.$refs.shipping_region) {
                            this.$refs.shipping_region.value = regionName;
                        }
                    });
                },
                setShippingCountry(countrySelectElement, countryCode, initialRegionName = '') {
                    if (!countrySelectElement) return;
                     const selectedOption = Array.from(countrySelectElement.options).find(opt => opt.value === countryCode);
                    countrySelectElement.value = countryCode; // Ensure it's selected

                    const countryData = this.shippingDirectoryData[countryCode] || false;

                    if (!countryData) {
                        this.setShippingRegionInputValue('');
                        this.shippingAvailableRegions = {};
                        this.shippingSelectedRegion = '0';
                        this.isShippingRegionRequired = false;
                        this.isShippingZipRequired = true;
                        return;
                    }

                    this.isShippingZipRequired = selectedOption ? selectedOption.dataset.isZipRequired === '1' : true;
                    this.isShippingRegionRequired = selectedOption ? selectedOption.dataset.isRegionRequired === '1' : true;
                    this.shippingAvailableRegions = countryData.regions || {};

                    const initialRegion = Object.entries(this.shippingAvailableRegions).find(([id, region]) => region.name === initialRegionName || region.code === initialRegionName);
                    this.shippingSelectedRegion = initialRegion ? initialRegion[0] : '0';
                    this.setShippingRegionInputValue(initialRegion && this.shippingAvailableRegions[initialRegion[0]] ? this.shippingAvailableRegions[initialRegion[0]].name : '');
                },
                changeShippingCountry(countrySelectElement) {
                    this.setShippingCountry(countrySelectElement, countrySelectElement.value);
                    this.validateShippingCountryDependentFields();
                },
                validateShippingCountryDependentFields() {
                    this.$nextTick(() => {
                        if (this.fields.shipping_postcode) this.removeMessages(this.fields.shipping_postcode);
                        if (this.fields.shipping_region) this.removeMessages(this.fields.shipping_region);
                        delete this.fields.shipping_postcode;
                        delete this.fields.shipping_region;
                        if (this.$refs.shipping_country_id) this.setupField(this.$refs.shipping_country_id);
                        if (this.$refs.shipping_postcode) this.setupField(this.$refs.shipping_postcode);
                        if (this.$refs.shipping_region) this.setupField(this.$refs.shipping_region);
                        if (this.$refs.shipping_region_id) this.setupField(this.$refs.shipping_region_id);


                        if (this.fields.shipping_postcode) this.validateField(this.fields.shipping_postcode);
                        if (this.fields.shipping_region) this.validateField(this.fields.shipping_region);
                        if (this.fields.shipping_region_id) this.validateField(this.fields.shipping_region_id);
                    });
                },
                hasShippingAvailableRegions() {
                    return Object.keys(this.shippingAvailableRegions).length > 0;
                }
                <?php endif; ?>
            }
        }

        window.addEventListener('DOMContentLoaded', () => {

            hyva.formValidation.addRule('telephone', (value, options) => {
                const phoneNumber = value.trim().replace(' ', '');
                if (phoneNumber && phoneNumber.length < (options.minlength || 3)) {
                    return '<?= $escaper->escapeJs(__('The telephone number is too short.')) ?>';
                }

                return true;
            });

            const postCodeSpecs = <?= /* @noEscape */ $block->getPostCodeConfig()->getSerializedPostCodes() ?>;

            hyva.formValidation.addRule('postcode', (postCode, options, field, context) => {
                context.removeMessages(field, 'postcode-warning');
                let countryId = '';
                let countryFieldRef = '';

                if (field.name === 'postcode') {
                    countryFieldRef = 'country_id';
                } else if (field.name === 'shipping_postcode') {
                    countryFieldRef = 'shipping_country_id';
                }

                if (countryFieldRef && context.fields[countryFieldRef] && context.fields[countryFieldRef].element) {
                    countryId = context.fields[countryFieldRef].element.value;
                }

                const validatedPostCodeExamples = [];
                const countryPostCodeSpecs = countryId && postCodeSpecs ? postCodeSpecs[countryId] : false;

                if (!postCode || !countryPostCodeSpecs) return true; // No validation if no postcode or no specs for country

                for (const postCodeSpec of Object.values(countryPostCodeSpecs)) {
                    if (new RegExp(postCodeSpec.pattern).test(postCode)) return true;
                    validatedPostCodeExamples.push(postCodeSpec.example);
                }
                if (validatedPostCodeExamples) {
                    context.addMessages(field, 'postcode-warning', [
                        '<?= $escaper->escapeJs(__('Provided Zip/Postal Code seems to be invalid.')) ?>',
                        '<?= $escaper->escapeJs(__(' Example: ')) ?>' + validatedPostCodeExamples.join('; ') + '. ',
                        '<?= $escaper->escapeJs(__('If you believe it is the right one you can ignore this notice.')) ?>'
                    ]);
                }

                return true;
            });
        })
    </script>
</div>
