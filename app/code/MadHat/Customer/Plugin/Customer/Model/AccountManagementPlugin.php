<?php
declare(strict_types=1);

namespace MadHat\Customer\Plugin\Customer\Model;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\AccountManagement;
use Magento\Framework\App\RequestInterface;
use MadHat\Customer\Helper\Data as CustomerHelper;

class AccountManagementPlugin
{
    /**
     * @var RequestInterface
     */
    private RequestInterface $request;

    /**
     * @var CustomerHelper
     */
    private CustomerHelper $customerHelper;

    /**
     * @param RequestInterface $request
     * @param CustomerHelper $customerHelper
     */
    public function __construct(
        RequestInterface $request,
        CustomerHelper $customerHelper
    ) {
        $this->request = $request;
        $this->customerHelper = $customerHelper;
    }

    /**
     * Save invoice email during customer creation
     *
     * @param AccountManagement $subject
     * @param CustomerInterface $result
     * @param CustomerInterface $customer
     * @param string|null $password
     * @param string|null $redirectUrl
     * @return CustomerInterface
     */
    public function afterCreateAccount(
        AccountManagement $subject,
        CustomerInterface $result,
        CustomerInterface $customer,
        ?string $password = null,
        ?string $redirectUrl = null
    ): CustomerInterface {
        if (!$this->customerHelper->isInvoiceEmailEnabled()) {
            return $result;
        }

        $invoiceEmail = $this->request->getParam('invoice_email');
        if ($invoiceEmail) {
            $result->setCustomAttribute('invoice_email', $invoiceEmail);
            // The customer will be saved by Magento after this plugin
        }

        return $result;
    }

    /**
     * Save invoice email during customer password change
     *
     * @param AccountManagement $subject
     * @param bool $result
     * @param string $email
     * @param string $currentPassword
     * @param string $newPassword
     * @return bool
     */
    public function afterChangePassword(
        AccountManagement $subject,
        bool $result,
        string $email,
        string $currentPassword,
        string $newPassword
    ): bool {
        if ($result && $this->customerHelper->isInvoiceEmailEnabled()) {
            $invoiceEmail = $this->request->getParam('invoice_email');
            if ($invoiceEmail !== null) {
                // Get the customer and update the invoice_email attribute
                try {
                    $customer = $subject->getCustomerRepository()->get($email);
                    $customer->setCustomAttribute('invoice_email', $invoiceEmail);
                    $subject->getCustomerRepository()->save($customer);
                } catch (\Exception $e) {
                    // Log error but don't fail the password change
                    error_log('Failed to save invoice_email during password change: ' . $e->getMessage());
                }
            }
        }

        return $result;
    }
}
